using commercetools.Base.Serialization;
using commercetools.Sdk.Api.Models.Messages;
using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Serialization;
using IT.Microservices.OrderReactor.Domain;
using IT.Microservices.OrderReactor.Infrastructure.Settings;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;
using IT.SharedLibraries.CT.Settings;
using ITF.SharedLibraries.Alerting;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Group.Order;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;


namespace IT.Microservices.OrderReactor.Application
{
    public class OrderUseCase(ILogger<OrderUseCase> logger, IOrderService orderService,
        IFloristOrderPerDayRepository iFloristOrderPerDayRepository, IFloristsRepository iFloristsRepository, ISequenceGeneratorService sequenceGeneratorService,
        IKafkaPublisherHelper kafkaPublisher, IOptionsMonitor<KafkaTopicsSettings> kafkaTopicsSettings, IOptionsMonitor<CommerceToolCustomSettings> commerceToolCustomSettings,
        IOptionsMonitor<OrderReactorSettings> orderReactorSettings, SerializerService serializerService , ISlackAlertService slackAlertService) : IOrderUseCase
    {
        public async Task SychronizeProcess(LegacyOrderCreatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderCreatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                var order = await CreateOrderProcess(message);

            }
            catch (commercetools.Base.Client.Error.BadRequestException brex)
            {
                bool rethrow = true;
                if (brex.ResponseBody != null && brex.ResponseBody is commercetools.Sdk.Api.Models.Errors.ErrorResponse)
                {
                    var errorResponse = brex.ResponseBody as commercetools.Sdk.Api.Models.Errors.ErrorResponse;
                    if (errorResponse.Errors.Any(e => e is commercetools.Sdk.Api.Models.Errors.DuplicateFieldError && (e as commercetools.Sdk.Api.Models.Errors.DuplicateFieldError).Code == "DuplicateField" && (e as commercetools.Sdk.Api.Models.Errors.DuplicateFieldError).Field == "orderNumber"))
                    {
                        logger.LogWarning(brex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Reason: order with same orderNumber already exists in CT", nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize());
                        rethrow = false;
                    }
                }

                if (rethrow)
                {
                    logger.LogWarning(brex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Message: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize(), brex.ToString());
                    await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCreatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {brex}", brex) ?? Task.CompletedTask);

                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCreatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAssignedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAssignedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                await AssignOrderProcess(message);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAssignedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderCancelledMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderCancelledMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderCancelled orderCancelled = message;
                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderCancelled, orderCancelled);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("CANCELLED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Order {orderBeforeUpdate.OrderNumber} has been updated as CANCELLED")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} status updated to CANCELLED");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCancelledMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCancelledMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryTimeUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryTimeUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveryTimeUpdated orderDeliveryTimeUpdated = message;
                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderDeliveryTimeUpdated, orderDeliveryTimeUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY TIME UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery time of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryTimeUpdated.Moment} / {orderDeliveryTimeUpdated.Time ?? "null"}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery times updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryTimeUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryTimeUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryStatusUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryStatusUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveryStatusUpdated orderDeliveryStatusUpdated = message;
                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderIdentifier);// handle some case when orderNumber is provided instead of orderCtId which is the one required for that process 
                if (order != null)
                    orderDeliveryStatusUpdated.OrderIdentifier = order.Id;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderDeliveryStatusUpdated, orderDeliveryStatusUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY STATUS UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery status of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryStatusUpdated.DeliveryStatus}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery status updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryStatusUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryStatusUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryCostUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryCostUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveryCostUpdated orderDeliveryCostUpdated = message;
                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderIdentifier);// handle some case when orderNumber is provided instead of orderCtId which is the one required for that process 
                if (order != null)
                    orderDeliveryCostUpdated.OrderIdentifier = order.Id;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderDeliveryCostUpdated, orderDeliveryCostUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY COST UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery cost of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryCostUpdated.DeliveryCost}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery cost updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryCostUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryCostUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryDateUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryDateUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderDeliveryDateUpdated orderDeliveryDateUpdated = message;
                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderDeliveryDateUpdated, orderDeliveryDateUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY DATE UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery date of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryDateUpdated.Date.ToString("yyyyMMdd")}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery date updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryDateUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryDateUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderCardMessageUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderCardMessageUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderCardMessageUpdated orderCardMessageUpdated = message;
                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderCardMessageUpdated, orderCardMessageUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("CARD MESSAGE UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Card Message of the order {orderBeforeUpdate.OrderNumber} has been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} card message updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCardMessageUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCardMessageUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderNotesUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderNotesUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderNotesUpdated orderNotesUpdated = message;
                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderNotesUpdated, orderNotesUpdated);
                if (orderBeforeUpdate != null)
                {

                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("NOTES UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Notes of the order {orderBeforeUpdate.OrderNumber} has been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} notes updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderNotesUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderNotesUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryAddressUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryAddressUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderDeliveryAddressUpdated orderDeliveryAddressUpdated = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderDeliveryAddressUpdated, orderDeliveryAddressUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY ADDRESS UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery address of the order {orderBeforeUpdate.OrderNumber} has been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryAddressUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryAddressUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveredOnBehalfMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveredOnBehalfMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveredOnBehalf globalOrderDelivered = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderDeliveredOnBehalf, globalOrderDelivered);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERED ON BEHALF DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as DELIVERED on behalf")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveredOnBehalfMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveredOnBehalfMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAcceptedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAcceptedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");
                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                }

                GlobalOrderAccepted globalOrderAccepted = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderAccepted, globalOrderAccepted);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ACCEPTED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as ACCEPTED by florist {globalOrderAccepted.FloristIdentier}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAcceptedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAcceptedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveredMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveredMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");

                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                }

                GlobalOrderDelivered globalOrderDelivered = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderDelivered, globalOrderDelivered);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as DELIVERED by florist {globalOrderDelivered.FloristIdentier}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveredMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveredMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRejectedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRejectedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");
                    return;
                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                    return;
                }

                GlobalOrderRejected globalOrderRejected = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderRejected, globalOrderRejected);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("REFUSED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as REFUSED by florist {globalOrderRejected.FloristIdentier}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRejectedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRejectedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderItemUpdatedMessage message)
        {
            try
            {
              

                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderItemUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message.Payload?.InhibOrderReactorProcess ?? false)
                {
                    logger.LogInformation("Fr case : We not process the payload because of InhibOrderReactorProcess in order to avoid duplicate process {Name}", nameof(orderService.UpdateProductFields));
                    return;
                }
                GlobalOrderItemUpdated orderItemUpdated = message;
                if (orderItemUpdated == default)
                {
                    logger.LogWarning("The OrderItemTypeEnum field is null");
                    return;
                }

                await orderService.Initialize();

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderItemUpdated, orderItemUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ITEM UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The items within the order {orderBeforeUpdate.OrderNumber} have been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} order item updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }
                
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderItemUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderItemUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderItemExecutorAmountUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderItemExecutorAmountUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderItemExecutorAmountUpdated orderItemExecutorAmountUpdated = message;
                if (orderItemExecutorAmountUpdated == default)
                {
                    logger.LogWarning("The OrderItemTypeEnum field is null");
                    return;
                }

                //await _orderService.Initialize();

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderItemExecutorAmountUpdated, orderItemExecutorAmountUpdated);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ITEM EXECUTOR AMOUNT UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The items within the order {orderBeforeUpdate.OrderNumber} have been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} order item executor amount updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderItemExecutorAmountUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderItemExecutorAmountUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAssignationRemovedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAssignationRemovedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");
                    return;
                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                    return;
                }

                GlobalOrderAssignationRemoved globalOrderAssignationRemoved = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderAssignationRemoved, globalOrderAssignationRemoved);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ASSIGNATION REMOVED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} is not assigned anymore to any florist")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} assignation removed");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAssignationRemovedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAssignationRemovedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAcceptedOnBehalfMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAcceptedOnBehalfMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderAcceptedOnBehalf globalOrderAccepted = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderAcceptedOnBehalf, globalOrderAccepted);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ACCEPTED ON BEHALF DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as ACCEPTED on behalf")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} updated to accepted");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAcceptedOnBehalfMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAcceptedOnBehalfMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRejectedOnBehalfMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRejectedOnBehalfMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRejectedOnBehalf globalOrderRejected = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderRejectedOnBehalf, globalOrderRejected);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("REFUSED ON BEHALF DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as REFUSED on behalf")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} updated to refused");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRejectedOnBehalfMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRejectedOnBehalfMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderSentMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderSentMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderSent globalOrderSent = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderSent, globalOrderSent);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("SENT DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} updated to accepted");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderSentMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderSentMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRecipientNameUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRecipientNameUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRecipientNameUpdated globalObject = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderRecipientNameUpdated, globalObject);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("RECIPIENT NAME UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The recipient name for order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Recipient name for order with id={message?.Payload?.OrderIdentifier} updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRecipientNameUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRecipientNameUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRecipientPhoneNumberUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRecipientPhoneNumberUpdated globalOrderSent = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderRecipientPhoneNumberUpdated, globalOrderSent);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("RECIPIENT PHONE NUMBER UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The recipient phone number for order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Recipient phone number for order with id={message?.Payload?.OrderIdentifier} updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRecipientCoordinatesUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRecipientCoordinatesUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRecipientCoordinatesUpdated globalOrderSent = message;

                IOrder orderBeforeUpdate = await Handle409Conflict(orderService.HandleOrderRecipientCoordinatesUpdated, globalOrderSent);
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("COORDINATES UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The coordinates for order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Coordinates for order with id={message?.Payload?.OrderIdentifier} updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRecipientCoordinatesUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRecipientCoordinatesUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }
        private async Task<IOrder> Handle409Conflict<T>(Func<T, Task<IOrder>> method, T param)
        {
            IOrder orderBeforeUpdate = null;
            try
            {
                orderBeforeUpdate = await method(param);
            }
            catch (commercetools.Base.Client.Error.ConcurrentModificationException ex)
            {
                logger.LogWarning($"409 response received, wait 50ms and retry: {ex.Message}");
                System.Threading.Thread.Sleep(500);
                orderBeforeUpdate = await method(param);
            }
            return orderBeforeUpdate;
        }

        #region RAO France Legacy Messages

        public async Task SychronizeProcess(OrderPlacedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderPlacedMessage));

                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID

                if (order != null)
                {
                    //If origin is from a commerceTools Reactor => order already exists, need to update instead of create
                    if (message.Payload.Source.Equals("CommerceTools", StringComparison.OrdinalIgnoreCase) || message.Payload.Source.Equals("PFS", StringComparison.OrdinalIgnoreCase))
                        logger.LogInformation("OrderId {OrderId} is CommerceTools source order, just updated instead", message.Payload.OrderId);

                    else
                        logger.LogWarning("OrderId {OrderId} already exists in CommerceTools", message.Payload.OrderId);

                    var orderUpdatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<OrderUpdatedMessage, OrderUpdatedPayload, OrderPlacedMessage, OrderPlacedPayload>(message);
                    await UpdateOrderProcess(orderUpdatedMessage, order);
                }
                else
                {
                    var legacyOrderCreatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderCreatedMessage, LegacyOrderCreatedPayload, OrderPlacedMessage, OrderPlacedPayload>(message);
                    await CreateOrderProcess(legacyOrderCreatedMessage);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderPlacedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderPlacedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}" , ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderAssignmentMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderAssignmentMessage));

                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID

   
                if (order != null)
                {
                    if (message.Payload.Emission.AssignmentState.Equals("UNASSIGNABLE", StringComparison.OrdinalIgnoreCase))
                    {
                        var orderStatusUpdate = orderService.CreateOrderUpdate(order, [new KeyValuePair<OrderDifference, object>(OrderDifference.Status, StatusEnum.ASSIGNATION_NOT_POSSIBLE.ToString())]);
                        await orderService.PostOrderUpdateWithRetry(orderStatusUpdate, order.Id, order.OrderNumber);
                        return;
                    }
                    //If origin is from a commerceTools Reactor => order already exists, need to update instead of create
                    if (message.Payload.Source.Equals("CommerceTools", StringComparison.OrdinalIgnoreCase) || message.Payload.Source.Equals("PFS", StringComparison.OrdinalIgnoreCase))
                        logger.LogInformation("OrderId {OrderId} is CommerceTools source order, just updated instead", message.Payload.OrderId);

                    else
                        logger.LogWarning("OrderId {OrderId} already exists in CommerceTools", message.Payload.OrderId);

                    var orderUpdatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<OrderUpdatedMessage, OrderUpdatedPayload, OrderAssignmentMessage, OrderAssignmentPayload>(message);
                    await UpdateOrderProcess(orderUpdatedMessage, order);
                   
                }
                else
                {
                    if (message.Payload.Emission.AssignmentState.Equals("UNASSIGNABLE", StringComparison.OrdinalIgnoreCase))
                    {
                        logger.LogError("Can't process UNASSIGNABLE OrderId {OrderId} because products are empty so we can't perform the creation process to CommerceTools", message.Payload.OrderId);
                    }
                    else
                    {
                        var legacyOrderCreatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderCreatedMessage, LegacyOrderCreatedPayload, OrderAssignmentMessage, OrderAssignmentPayload>(message);
                        order = await CreateOrderProcess(legacyOrderCreatedMessage);
                    }
                }

                if (order != null)
                {
                    if (message.Payload.Emission.AssignmentState.Equals("ASSIGNED", StringComparison.OrdinalIgnoreCase))
                    {
                        var legacyOrderAssignedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderAssignedMessage, LegacyOrderAssignedPayload, OrderAssignmentMessage, OrderAssignmentPayload>(message, order.Id);
                        await kafkaPublisher.Publish<LegacyOrderAssignedMessage>(legacyOrderAssignedMessage, kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId);
                    }
                    else await (message.Payload.Emission.AssignmentState switch
                    {
                        "REJECTED" => kafkaPublisher.Publish<LegacyOrderAssignationRemovedMessage>((LegacyOrderAssignationRemovedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        "CANCELED" => kafkaPublisher.Publish<LegacyOrderCancelledMessage>((LegacyOrderCancelledMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        _ => Task.CompletedTask
                    });
                }
                else
                    logger.LogWarning("AssignProcess skipped for order {OrderId} because order is null", message.Payload.OrderId);

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderAssignmentMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderAssignmentMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderUpdatedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderUpdatedMessage));



                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogWarning("Order with id {OrderIdentifier} NOT found, try to create", message?.Payload?.OrderId);
                    var legacyOrderCreatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderCreatedMessage, LegacyOrderCreatedPayload, OrderUpdatedMessage, OrderUpdatedPayload>(message);
                    order = await CreateOrderProcess(legacyOrderCreatedMessage);
                }
                else
                    await UpdateOrderProcess(message, order);


            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderUpdatedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderUpdatedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderManagementStatusMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderManagementStatusMessage));


                await orderService.HandleRAOLegacyOrderManagementStatus(message);

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderManagementStatusMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderManagementStatusMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);

            }
        }

        public async Task SychronizeProcess(InvoiceMessage message)
        {
            try
            {
                if (message?.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type} for floristId {floristId}", message.Payload.OrderId, nameof(InvoiceMessage) , message.Payload.FloristId);

                var order = await orderService.GetByOrderNumber(message.Payload.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogError("Order with id {OrderIdentifier} NOT found in CT, we cant save the Florist order invoice : florist id : {florist}", message.Payload.OrderId, message.Payload.FloristId);
                    await (slackAlertService?.SendErrorAlertAsync($"Order with id {message.Payload.OrderId} NOT found in CT, we cant save the Florist order invoice : florist id : {message.Payload.FloristId} , full message : {message.Serialize()}") ?? Task.CompletedTask);
                }
                else
                {
                    var actions = new List<IOrderUpdateAction>
                    {
                        new OrderSetCustomFieldAction
                        {
                            Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_INVOICE_URL,
                            Value = message.Payload.Url
                        }
                    };

                    await orderService.PostOrderUpdateWithRetry(new OrderUpdate { Version = order.Version, Actions = actions }, order.Id, order.OrderNumber);

                    logger.LogInformation("Order with id {OrderIdentifier} updated in CT with invoice url {url} for floristId {floristId}", message.Payload.OrderId, message.Payload.Url , message.Payload.FloristId);
                }
                    


            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {ex}", nameof(SychronizeProcess), nameof(InvoiceMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(InvoiceMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderDeliveryCourierUpdatedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderDeliveryCourierUpdatedMessage));



                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogWarning("Order with id {OrderIdentifier} NOT found", message?.Payload?.OrderId);
                    return;
                }

                string strEnumType = message.Payload.DeliveryCourierStatus.ToUpper() switch
                {
                    "BOOKED" => DeliveryStatusEnum.BOOKED.ToString(),
                    "CANCELED" => DeliveryStatusEnum.PENDING.ToString(),
                    "ADDED" => DeliveryStatusEnum.SCHEDULED.ToString(),
                    "GOING_TO_DRIVE" => DeliveryStatusEnum.PICKING.ToString(),
                    "SHOW_REFERENCE" => DeliveryStatusEnum.ALMOST_PICKING.ToString(),
                    "ARRIVED_AT_DRIVE" => DeliveryStatusEnum.WAITING_AT_PICKUP.ToString(),
                    "GOING_TO_CLIENT" => DeliveryStatusEnum.DELIVERING.ToString(),
                    "ARRIVED_AT_CLIENT" => DeliveryStatusEnum.ALMOST_DELIVERING.ToString(),
                    "VALIDATED" => DeliveryStatusEnum.DELIVERED.ToString(),
                    "REMOVED" => DeliveryStatusEnum.NO_COURIER_AVAILABLE.ToString(),
                    "DELIVERY_INCIDENT" => DeliveryStatusEnum.CANCELLED.ToString(),
                    _ => message.Payload.DeliveryCourierStatus  // Default case
                };

                if (Enum.TryParse(strEnumType.ToUpper(), out DeliveryStatusEnum deliveryStatusEnum))
                {
                    await SychronizeProcess((LegacyOrderDeliveryStatusUpdatedMessage)(order.Id, message, deliveryStatusEnum));
                    await SychronizeProcess((LegacyOrderDeliveryCostUpdatedMessage)(order.Id, message));
                }
                else
                    logger.LogError("Failed to process {process} on message {statusMessage} and {costMmessage} for order {orderIdentifier}. Payload : {payload}. Reason: unable to find mapping match for status : {status}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryStatusUpdatedMessage), nameof(LegacyOrderDeliveryCostUpdatedMessage), message?.Payload?.OrderId, message?.Serialize(), message.Payload.DeliveryCourierStatus);



            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderDeliveryCourierUpdatedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderDeliveryCourierUpdatedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderDeliveryCourierResetedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderDeliveryCourierResetedMessage));



                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogWarning("Order with id {OrderIdentifier} NOT found", message?.Payload?.OrderId);
                    return;
                }

                await SychronizeProcess((LegacyOrderDeliveryStatusUpdatedMessage)(order.Id, message));

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderDeliveryCourierResetedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderDeliveryCourierResetedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderDeliveryCourierInitializedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderDeliveryCourierInitializedMessage));



                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogError("At this step, order must be exists in CommerceTools, but order with id {OrderIdentifier} NOT found", message?.Payload?.OrderId);
                    return;
                }

                await SychronizeProcess((LegacyOrderDeliveryStatusUpdatedMessage)(order.Id, message));

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderDeliveryCourierInitializedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderDeliveryCourierInitializedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        #endregion

        private async Task UpdateFloristCounterPerDay(GlobalFloristOrderPerDayModel globalFloristOrderPerDay)
        {
            if (globalFloristOrderPerDay.Counter == 1)
            {
                await iFloristOrderPerDayRepository.InsertOneAsync(globalFloristOrderPerDay);
            }
            else
            {
                await iFloristOrderPerDayRepository.Update(globalFloristOrderPerDay);
            }
        }

        private async Task<GetFloristCounterPerDayResult> GetFloristCounterPerDay(LegacyOrderAssignedPayload legacyOrderAssigned)
        {
            GetFloristCounterPerDayResult result = new GetFloristCounterPerDayResult { Counter = -1, OrderDeliveryDate = DateTime.MinValue };
            IOrder order = null;
            if (legacyOrderAssigned != null && !String.IsNullOrWhiteSpace(legacyOrderAssigned.OrderIdentifier) && !String.IsNullOrWhiteSpace(legacyOrderAssigned.FloristIdentifier))
            {
                order = await orderService.GetById(legacyOrderAssigned.OrderIdentifier);
                if (order != null)
                {
                    DateTime deliveryDate = DateTime.MinValue;
                    string strDeliveryDate = null;

                    if (order?.ShippingAddress?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.ShippingAddress.DATE) ?? false)
                        strDeliveryDate = order.ShippingAddress.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.DATE].ToString();

                    // evaluate next internal order id
                    if (DateTime.TryParseExact(strDeliveryDate, "yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out deliveryDate))
                    {
                        result.Counter = await iFloristOrderPerDayRepository.GetCounter(legacyOrderAssigned.FloristIdentifier, deliveryDate);
                        result.OrderDeliveryDate = deliveryDate;
                    }
                    else
                    {
                        logger.LogWarning($"The internalOrderId cannot be evaluated for order with id={legacyOrderAssigned.OrderIdentifier} and florist with id={legacyOrderAssigned.FloristIdentifier} because the custom fields date is empty [order.shippingAddress.custom.fields.date] ");
                    }
                }
            }
            return result;
        }
        private async Task<IOrder> CreateOrderProcess(LegacyOrderCreatedMessage message)
        {
            await orderService.Initialize();
            if (String.IsNullOrWhiteSpace(message?.Payload?.OrderNumber))
            {
                long nextOrderNumber = await sequenceGeneratorService.GetNext();
                if (nextOrderNumber > 0)
                {
                    message.Payload.OrderNumber = nextOrderNumber.ToString();
                }
            }

            GlobalOrderModel globalOrderModel = message;
            IOrder order = await orderService.HandleOrderCreated(globalOrderModel);
            if (order == null)
            {
                logger.LogWarning("The message with id {id} failed in the process {process} on message {message} for order {Id}. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize());
            }
            else
            {

                string[] countryCodesAddDocSender = new[] { "IT2" };
                if (countryCodesAddDocSender.Contains(orderReactorSettings?.CurrentValue?.CountryCode))
                {
                    GlobalFloristModel transmittorFlorist = null;
                    if (!string.IsNullOrWhiteSpace(message?.Payload?.SenderFloristIdentifier))
                    {
                        transmittorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.SenderFloristIdentifier);
                        if (transmittorFlorist == null)
                        {
                            logger.LogWarning("{Message} : SenderFloristIdentifier {Florist} is unknown, OrderNumber : {OrderNumber}", nameof(LegacyOrderCreatedMessage), message.Payload.SenderFloristIdentifier, message.Payload.LegacyOrderNumber);
                        }
                        else
                        {
                            string url = orderReactorSettings?.CurrentValue?.PFsGetOrderDocumentOrderUrlFormat
                            .Replace("{orderIdentifier}", order.Id)
                            .Replace("{floristIdentifier}", transmittorFlorist.Id)
                            .Replace("{type}", ((int)ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_SENDER).ToString());

                            transmittorFlorist.Documents.Add(new Document
                            {
                                CTOrderId = order.Id,
                                DocType = ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_SENDER,
                                FileExtension = "pdf",
                                FileName = order.OrderNumber + ".pdf",
                                OctopusOrderId = order.OrderNumber,
                                OrderReference = order.Id,
                                Month = order.CreatedAt.Month,
                                Year = order.CreatedAt.Year,
                                Url = url
                            });

                            await iFloristsRepository.SaveDocuments(transmittorFlorist);
                        }
                    }


                }

                OrderNewHistoryRecordMessageBuilder historyBuilder = new OrderNewHistoryRecordMessageBuilder();
                historyBuilder.AddCommerceToolsID(order.Id)
                    .AddOrderNumber(order.OrderNumber)
                    .AddInitialOrderStatus(order.GetFloristOrderStatus())
                    .AddExecutingFloristId(order.GetExecutingFloristId())
                    .AddOrderAmount(order.GetTotalItemsPrice() + order.GetDeliveryPrice())
                    .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(globalOrderModel))
                    .AddOrderAction("ORDER NUMBER GENERATED")
                    .AddMessage($"Created order in CT for legacy order {order.GetLegacyOrderNumber()} with OrderNumber {order.OrderNumber} and CommerceToolsOrderID {order.Id}")
                    .AddCtOrderPreUpdate(order?.Serialize(SerializerType.CommerceTools, serializerService));

                await kafkaPublisher.Publish(historyBuilder.Build(), kafkaTopicsSettings.CurrentValue.Order);
            }
            return order;
        }

        private async Task UpdateOrderProcess(OrderUpdatedMessage message, IOrder order)
        {
            var fieldsUpdated = orderService.GetDifferencesForUpdate(message.Payload, order);
            foreach (var field in fieldsUpdated)
            {
                await (field.Key switch
                {
                    OrderDifference.DeliveryMomentTime => kafkaPublisher.Publish<LegacyOrderDeliveryTimeUpdatedMessage>((LegacyOrderDeliveryTimeUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.DeliveryDate => kafkaPublisher.Publish<LegacyOrderDeliveryDateUpdatedMessage>((LegacyOrderDeliveryDateUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.RecipientCoordinates => kafkaPublisher.Publish<LegacyOrderRecipientCoordinatesUpdatedMessage>((LegacyOrderRecipientCoordinatesUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.DeliveryAddress => kafkaPublisher.Publish<LegacyOrderDeliveryAddressUpdatedMessage>((LegacyOrderDeliveryAddressUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.DeliveryInstructions => kafkaPublisher.Publish<LegacyOrderNotesUpdatedMessage>((LegacyOrderNotesUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.Message => kafkaPublisher.Publish<LegacyOrderCardMessageUpdatedMessage>((LegacyOrderCardMessageUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.RecipientPhoneNumber => kafkaPublisher.Publish<LegacyOrderRecipientPhoneNumberUpdatedMessage>((LegacyOrderRecipientPhoneNumberUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.RecipientFullName => kafkaPublisher.Publish<LegacyOrderRecipientNameUpdatedMessage>((LegacyOrderRecipientNameUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.NewProduct => kafkaPublisher.Publish<LegacyOrderItemUpdatedMessage>((LegacyOrderItemUpdatedMessage)(order.Id, message, (ProductInformations)field.Value, OrderItemTypeEnum.NEW, false), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.DeleteProduct => kafkaPublisher.Publish<LegacyOrderItemUpdatedMessage>((LegacyOrderItemUpdatedMessage)(order.Id, message, (ProductInformations)field.Value, OrderItemTypeEnum.DELETE, false), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.UpdateProduct => kafkaPublisher.Publish<LegacyOrderItemUpdatedMessage>((LegacyOrderItemUpdatedMessage)(order.Id, message, (ProductInformations)field.Value, OrderItemTypeEnum.UPDATE, true), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                    OrderDifference.UpdateCTFieldsProductList => orderService.UpdateProductFields(order, (List<ProductInformations>)field.Value),

                    _ => Task.CompletedTask
                });
            }
            await (message.Payload.Status.Status switch
            {
                "LFM" => SychronizeProcess((LegacyOrderDeliveredMessage)(order.Id, message)),
                "LFA" => SychronizeProcess((LegacyOrderDeliveredMessage)(order.Id, message)),
                "AFF" => (string.IsNullOrEmpty(message.Payload.InternationalOrderId) && !string.Equals(message.Payload.Recipient.CountryCode, "FR", StringComparison.OrdinalIgnoreCase)) ? SychronizeProcess((LegacyOrderSentMessage)(order.Id, message)) : Task.CompletedTask,
                _ => Task.CompletedTask
            });

            if (fieldsUpdated?.Count > 0)
                await orderService.HandleRAOLegacyOrderUpdate(message.Payload, order, fieldsUpdated);

        }

        private async Task AssignOrderProcess(LegacyOrderAssignedMessage message)
        {
            if (message?.Payload?.FloristIdentifier == default)
            {
                logger.LogWarning("FloristIdentifier is null");
            }
            var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
            if (executorFlorist == null)
            {
                logger.LogWarning("FloristIdentifier is unknown");
            }

            GetFloristCounterPerDayResult result = await GetFloristCounterPerDay(message?.Payload);
            if (result.Counter >= 0)
            {
                result.Counter++;
                string internalOrderId = result.OrderDeliveryDate.ToString("yyyyMMdd") + "-" + result.Counter.ToString();

                GlobalOrderAssigned orderAssigned = message;
                IOrder orderBeforeUpdate = null;
                
                StatusEnum defaultStatus = StatusEnum.ASSIGNED;

                if (orderReactorSettings?.CurrentValue?.CountryCode == "FR")
                    defaultStatus = StatusEnum.ACCEPTED;

                try
                {
                    logger.LogInformation("AssignOrderProcess => HandleOrderAssigned called for order {OrderId}", orderAssigned.OrderIdentifier);
                    orderBeforeUpdate = await orderService.HandleOrderAssigned(orderAssigned, internalOrderId, defaultStatus);
                }
                catch (commercetools.Base.Client.Error.ConcurrentModificationException ex)
                {
                    logger.LogWarning($"409 response received, wait 50ms and retry: {ex.Message}");
                    System.Threading.Thread.Sleep(500);
                    logger.LogInformation("AssignOrderProcess => HandleOrderAssigned called for order {OrderId} after 409 response", orderAssigned.OrderIdentifier);
                    orderBeforeUpdate = await orderService.HandleOrderAssigned(orderAssigned, internalOrderId, defaultStatus);
                }
                if (orderBeforeUpdate != null)
                {
                    GlobalFloristOrderPerDayModel globalFloristOrderPerDay = new GlobalFloristOrderPerDayModel
                    {
                        Counter = result.Counter,
                        FloristIdentifier = message?.Payload?.FloristIdentifier,
                        DeliveryDate = result.OrderDeliveryDate,

                    };
                    globalFloristOrderPerDay.StoreProjectedEvent(message.MessageId);
                    globalFloristOrderPerDay.SetLastUpdate();
                    globalFloristOrderPerDay.SetId();

                    await UpdateFloristCounterPerDay(globalFloristOrderPerDay);

                    string[] countryCodesAddDocReceiver = new[] { "ES", "PT", "IT2" };
                    if (countryCodesAddDocReceiver.Contains(orderReactorSettings?.CurrentValue?.CountryCode))
                    {
                        string url = orderReactorSettings?.CurrentValue?.PFsGetOrderDocumentOrderUrlFormat
                            .Replace("{orderIdentifier}", orderAssigned.OrderIdentifier)
                            .Replace("{floristIdentifier}", executorFlorist.Id)
                            .Replace("{type}", ((int)ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_RECEIVER).ToString());

                        executorFlorist.Documents.Add(new Document
                        {
                            CTOrderId = orderAssigned.OrderIdentifier,
                            DocType = ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_RECEIVER,
                            FileExtension = "pdf",
                            FileName = orderBeforeUpdate.OrderNumber + ".pdf",
                            OctopusOrderId = orderBeforeUpdate.OrderNumber,
                            OrderReference = orderAssigned.OrderIdentifier,
                            Month = orderBeforeUpdate.CreatedAt.Month,
                            Year = orderBeforeUpdate.CreatedAt.Year,
                            Url = url
                        });

                        await iFloristsRepository.SaveDocuments(executorFlorist);
                    }

                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                    .AddOrderAction("ASSIGNED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Order {orderBeforeUpdate.OrderNumber} has been updated as ASSIGNED to florist {message?.Payload?.FloristIdentifier}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation("The message with id {id} has been handled in the process {process} on message {message} for order {Id}. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize());
                }
                else
                {
                    logger.LogWarning("The message with id {id} failed in the process {process} on message {message} for order {Id}. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize());
                }
            }
            else
            {
                logger.LogWarning("The message with id {id} failed in the process {process} on message {message} for order {Id} becase evaluated internalOrderId is empty. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize());
            }
        }

    }
}
