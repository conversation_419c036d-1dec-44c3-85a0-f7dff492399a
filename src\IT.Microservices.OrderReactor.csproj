﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>02f6ecbb-6e11-43c0-8694-105088e11f24</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
	<Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>IT.Microservices.OrderReactor.xml</DocumentationFile>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="ITE.Order.Library" Version="2.15.0" />
	  <PackageReference Include="ITF.Order.Library" Version="2.16.2" />
	  <PackageReference Include="ITF.SharedModels" Version="8.19.0" />
	  <PackageReference Include="ITF.SharedLibraries" Version="8.26.0" />
	  <PackageReference Include="IT.SharedLibraries.CT" Version="2.64.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\..\ITE.Order.Library\src\ITE.Order.Library.csproj" />
    <ProjectReference Include="..\..\ITF.Order.Library\src\ITF.Order.Library.csproj" />
    <ProjectReference Include="..\..\ITF.SharedModels\src\ITF.SharedModels.csproj" />
	  <ProjectReference Include="..\..\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj" />
	  <ProjectReference Include="..\..\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj" />
  </ItemGroup>
  
</Project>
