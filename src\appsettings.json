{
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information",
        "Elastic": "Warning",
        "Apm": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "IT.Microservices.OrderReactor"
    }
  },
  "ElasticApm": {
    "ServerUrl": "**********",
    "Enabled": true,
    "TransactionSampleRate": 1,
    "CaptureBody": "all",
    "CaptureHeaders": true,
    "SpanFramesMinDuration": 0, // no stacktrace except for exception
    "CloudProvider": "none"
  },
  "ElasticSearchLog": {
    "ElasticSearchLog": "**********"
  },
  "Unleash": {
    "Url": "**********",
    "ProjectId": "default",
    "ApplicationName": "IT.Microservices.OrderReactor",
    "FetchTogglesIntervalInSeconds": 15,
    "SendMetricsIntervalInSeconds": 30,
    "Environment": "development"
  },
  "FeatureFlags": {
    "Provider": "featuremanager"
  },
  "SlackAlert": {
    "ApiToken": "********************************************************",
    "DefaultChannel": "alerts-ms-fr",
    "SendMessageUrl": "https://slack.com/api/chat.postMessage",
    "BotName": "Error Alert Bot",
    "BotEmoji": ":warning:",
    "MaxRetryAttempts": 3,
    "RetryInitialDelayMs": 1000,
    "TimeoutMs": 10000,
    "IncludeStackTrace": true,
    "MaxMessageLength": 3000,
    "EnableFallbackLogging": true,
    "CircuitBreakerThreshold": 5,
    "CircuitBreakerDurationSeconds": 60
  }
}
