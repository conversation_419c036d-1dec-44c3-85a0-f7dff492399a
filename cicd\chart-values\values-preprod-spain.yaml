###
# Values for preprod environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/IT.Microservices.OrderReactor/"
#helm diff upgrade itorderreactor ${helmChartPath} --values chart-values/values-preprod-spain.yaml -n ite-ms --set 'image.tag=latest,image.repository=itepreprodacr.azurecr.io/itorderreactor'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "itorderreactor"
fullnameOverride: "itorderreactor"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # Vault policy match ms-* service account
  name: "ite-ms-itorderreactor"

dotnetProgramName: "IT.Microservices.OrderReactor.dll"
appStartCommand: "dotnet IT.Microservices.OrderReactor.dll"

podAnnotations:
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/itorderreactor.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"  
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "ite-microservices"
  # vault.hashicorp.com/agent-inject-secret-itorderreactor.pass: "applications/ite-microservices"

  # Inject secret via a configmap named itorderreactor-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'itorderreactor-secrets'
  
  #inject secret via env variables 
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  vault.hashicorp.com/role: 'ite-microservices'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/ite-microservices'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/ite-microservices" -}}
      export Client__ClientId={{ .Data.ite_microservice_write_id }}
      export Client__ClientSecret={{ .Data.ite_microservice_write_password }}
      export SlackAlert__ApiToken={{ .Data.slack_webook }}
    {{- end }}

service:
  type: ClusterIP
  port: 80

ingress:
  hosts: 
  - microservices.preprod.interflora.es
  path: "/itorderreactor"
  tls:
  - hosts:
    - microservices.preprod.interflora.es
    secretName: "preprod-interflora-es-cert"  
  enabled: true
  ingressClass: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/tls-acme: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/http2-push-preload: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://*.interflora.es"
    nginx.ingress.kubernetes.io/proxy-body-size: 6m
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      ###
      # Whitelist 
      ###
      satisfy any;
      ## MPLS Interflora
      allow *************/32;
      allow *************/32;
      allow ***************/32;
      deny all;
      ###
      # Redirections 
      ###
      rewrite ^/itorderreactor$ /itorderreactor/swagger/index.html redirect; 

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "50%"

livenessProbe:
  httpGet:
    path: /health
    port: 80
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 3
  
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 100

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 

  kafkaConfig: |-   
    "Kafka": {
      "SubscriberConfigurations": [
        {
          "AutoOffsetReset": 1,
          "ClassName": "OrderHandler",
          "EnableAutoCommit": false,
          "ManualCommitPeriod": 1,
          "EnablePartitionEof": false,
          "GroupId": "IT.Microservices.OrderReactor.consumer",
          "TopicName": "order",
          "Deserializer": "CommerceTools"
        }
      ]
    }

  kafkaTopicsSettings: |-   
    "KafkaTopicsSettings": {
      "Order": "order"
    }

  commonSettingsConfig: |-   
    "CommerceToolCustomSettings": {
      "LocalCountryCode": "ES",
      "LocalCountryChannelKey": "interflora.es",
      "LocalCountryStoreKey": "ITE",
      "CtMoruningProductTypeKey": "mourning",
      "LocalCountryAccessoriesCategoryKey": "ACC",
      "LocalCountryProductsCategoryKey": "category",
      "OutboundOrderShippingMethodKey": "pfs-international-spain",
      "MourningShippingMethodKey": "mourning",
      "PfsShippingMethodKey" : "pfs"
    }

  OrderReactorSettings: |-
    "OrderReactorSettings": {
      "CountryCode" : "ES",
      "PFsGetOrderDocumentOrderUrlFormat": "http://itmsdocuments.ite-ms/itmsdocuments/api/v1/GetOrderDoc?orderId={orderIdentifier}&floristId={floristIdentifier}&type={type}"
    }

  spanishOrderSettings: |-
    "SpanishOrderSettings": {
      "ITFPLUSproductKey": "ITFPLUS",
      "VatKey": "vat",
      "VatReducedKey": "vat-reduced",
      "PfsShippingMethodKey": "pfs",
      "PfsReducedShippingMethodKey": "pfs-reduced",
      "PfsMourningReducedShippingMethodKey": "reduced-mourning",
      "CityStates": [
        {
          "CityName": "Ceuta",
          "State": "ceuta"
        },
        {
          "CityName": "Melilla",
          "State": "melilla"
        }
      ],
      "States": [
        {
          "code": "16",
          "key": "basque country",
          "name": "País Basc"
        },
        {
          "code": "08",
          "key": "castilla-la mancha",
          "name": "Castella - la Manxa"
        },
        {
          "code": "10",
          "key": "valencian community",
          "name": "Comunitat Valenciana"
        },
        {
          "code": "01",
          "key": "andalusia",
          "name": "Andalusia"
        },
        {
          "code": "07",
          "key": "castile and leon",
          "name": "Castella i Lleó"
        },
        {
          "code": "11",
          "key": "extremadura",
          "name": "Extremadura"
        },
        {
          "code": "04",
          "key": "balearic islands",
          "name": "Illes Balears"
        },
        {
          "code": "09",
          "key": "catalonia",
          "name": "Catalunya"
        },
        {
          "code": "12",
          "key": "galicia",
          "name": "Galícia"
        },
        {
          "code": "02",
          "key": "aragon",
          "name": "Aragó"
        },
        {
          "code": "17",
          "key": "la rioja",
          "name": "La Rioja"
        },
        {
          "code": "13",
          "key": "community of madrid",
          "name": "Comunitat de Madrid"
        },
        {
          "code": "14",
          "key": "region of murcia",
          "name": "Regió de Múrcia"
        },
        {
          "code": "15",
          "key": "navarre",
          "name": "Comunitat Foral de Navarra"
        },
        {
          "code": "03",
          "key": "asturias",
          "name": "Principat d'Astúries"
        },
        {
          "code": "05",
          "key": "canary islands",
          "name": "Canàries"
        },
        {
          "code": "06",
          "key": "cantabria",
          "name": "Cantàbria"
        },
        {
          "code": "18",
          "key": "ceuta",
          "name": "Ceuta"
        },
        {
          "code": "19",
          "key": "melilla",
          "name": "Melilla"
        },
        {
          "code": "98",
          "name": "No consta"
        },
        {
          "code": "99",
          "name": "Altres/Diversos"
        }
      ],
      "Provinces": [
        {
          "code": "01",
          "name": "Àlaba",
          "stateCode": "16",
          "postalCodePrefixes": [ "01" ]
        },
        {
          "code": "02",
          "name": "Albacete",
          "stateCode": "08",
          "postalCodePrefixes": [ "02" ]
        },
        {
          "code": "03",
          "name": "Alacant",
          "stateCode": "10",
          "postalCodePrefixes": [ "03" ]
        },
        {
          "code": "04",
          "name": "Almeria",
          "stateCode": "01",
          "postalCodePrefixes": [ "04" ]
        },
        {
          "code": "05",
          "name": "Àvila",
          "stateCode": "07",
          "postalCodePrefixes": [ "05" ]
        },
        {
          "code": "06",
          "name": "Badajoz",
          "stateCode": "11",
          "postalCodePrefixes": [ "06" ]
        },
        {
          "code": "07",
          "name": "Illes Balears",
          "stateCode": "04",
          "postalCodePrefixes": [ "07" ]
        },
        {
          "code": "08",
          "name": "Barcelona",
          "stateCode": "09",
          "postalCodePrefixes": [ "08" ]
        },
        {
          "code": "09",
          "name": "Burgos",
          "stateCode": "07",
          "postalCodePrefixes": [ "09" ]
        },
        {
          "code": "10",
          "name": "Càceres",
          "stateCode": "11",
          "postalCodePrefixes": [ "10" ]
        },
        {
          "code": "11",
          "name": "Cadis",
          "stateCode": "01",
          "postalCodePrefixes": [ "11" ]
        },
        {
          "code": "12",
          "name": "Castelló de la Plana",
          "stateCode": "10",
          "postalCodePrefixes": [ "12" ]
        },
        {
          "code": "13",
          "name": "Ciudad Real",
          "stateCode": "08",
          "postalCodePrefixes": [ "13" ]
        },
        {
          "code": "14",
          "name": "Còrdova",
          "stateCode": "01",
          "postalCodePrefixes": [ "14" ]
        },
        {
          "code": "15",
          "name": "La Corunya",
          "stateCode": "12",
          "postalCodePrefixes": [ "15" ]
        },
        {
          "code": "16",
          "name": "Conca",
          "stateCode": "08",
          "postalCodePrefixes": [ "16" ]
        },
        {
          "code": "17",
          "name": "Girona",
          "stateCode": "09",
          "postalCodePrefixes": [ "17" ]
        },
        {
          "code": "18",
          "name": "Granada",
          "stateCode": "01",
          "postalCodePrefixes": [ "18" ]
        },
        {
          "code": "19",
          "name": "Guadalajara",
          "stateCode": "08",
          "postalCodePrefixes": [ "19" ]
        },
        {
          "code": "20",
          "name": "Guipúscoa",
          "stateCode": "16",
          "postalCodePrefixes": [ "20" ]
        },
        {
          "code": "21",
          "name": "Huelva",
          "stateCode": "01",
          "postalCodePrefixes": [ "21" ]
        },
        {
          "code": "22",
          "name": "Osca",
          "stateCode": "02",
          "postalCodePrefixes": [ "22" ]
        },
        {
          "code": "23",
          "name": "Jaén",
          "stateCode": "01",
          "postalCodePrefixes": [ "23" ]
        },
        {
          "code": "24",
          "name": "Lleó",
          "stateCode": "07",
          "postalCodePrefixes": [ "24" ]
        },
        {
          "code": "25",
          "name": "Lleida",
          "stateCode": "09",
          "postalCodePrefixes": [ "25" ]
        },
        {
          "code": "26",
          "name": "La Rioja",
          "stateCode": "17",
          "postalCodePrefixes": [ "26" ]
        },
        {
          "code": "27",
          "name": "Lugo",
          "stateCode": "12",
          "postalCodePrefixes": [ "27" ]
        },
        {
          "code": "28",
          "name": "Madrid",
          "stateCode": "13",
          "postalCodePrefixes": [ "28" ]
        },
        {
          "code": "29",
          "name": "Màlaga",
          "stateCode": "01",
          "postalCodePrefixes": [ "29" ]
        },
        {
          "code": "30",
          "name": "Múrcia",
          "stateCode": "14",
          "postalCodePrefixes": [ "30" ]
        },
        {
          "code": "31",
          "name": "Navarra",
          "stateCode": "15",
          "postalCodePrefixes": [ "31" ]
        },
        {
          "code": "32",
          "name": "Ourense",
          "stateCode": "12",
          "postalCodePrefixes": [ "32" ]
        },
        {
          "code": "33",
          "name": "Astúries",
          "stateCode": "03",
          "postalCodePrefixes": [ "33" ]
        },
        {
          "code": "34",
          "name": "Palència",
          "stateCode": "07",
          "postalCodePrefixes": [ "34" ]
        },
        {
          "code": "35",
          "name": "Las Palmas",
          "stateCode": "05",
          "postalCodePrefixes": [ "35" ]
        },
        {
          "code": "36",
          "name": "Pontevedra",
          "stateCode": "12",
          "postalCodePrefixes": [ "36" ]
        },
        {
          "code": "37",
          "name": "Salamanca",
          "stateCode": "07",
          "postalCodePrefixes": [ "37" ]
        },
        {
          "code": "38",
          "name": "Santa Cruz de Tenerife",
          "stateCode": "05",
          "postalCodePrefixes": [ "38" ]
        },
        {
          "code": "39",
          "name": "Cantàbria",
          "stateCode": "06",
          "postalCodePrefixes": [ "39" ]
        },
        {
          "code": "40",
          "name": "Segòvia",
          "stateCode": "07",
          "postalCodePrefixes": [ "40" ]
        },
        {
          "code": "41",
          "name": "Sevilla",
          "stateCode": "01",
          "postalCodePrefixes": [ "41" ]
        },
        {
          "code": "42",
          "name": "Sòria",
          "stateCode": "07",
          "postalCodePrefixes": [ "42" ]
        },
        {
          "code": "43",
          "name": "Tarragona",
          "stateCode": "09",
          "postalCodePrefixes": [ "43" ]
        },
        {
          "code": "44",
          "name": "Terol",
          "stateCode": "02",
          "postalCodePrefixes": [ "44" ]
        },
        {
          "code": "45",
          "name": "Toledo",
          "stateCode": "08",
          "postalCodePrefixes": [ "45" ]
        },
        {
          "code": "46",
          "name": "València",
          "stateCode": "10",
          "postalCodePrefixes": [ "46" ]
        },
        {
          "code": "47",
          "name": "Valladolid",
          "stateCode": "07",
          "postalCodePrefixes": [ "47" ]
        },
        {
          "code": "48",
          "name": "Biscaia",
          "stateCode": "16",
          "postalCodePrefixes": [ "48" ]
        },
        {
          "code": "49",
          "name": "Zamora",
          "stateCode": "07",
          "postalCodePrefixes": [ "49" ]
        },
        {
          "code": "50",
          "name": "Saragossa",
          "stateCode": "02",
          "postalCodePrefixes": [ "50" ]
        },
        {
          "code": "51",
          "name": "Ceuta",
          "stateCode": "18",
          "postalCodePrefixes": [ "51" ]
        },
        {
          "code": "52",
          "name": "Melilla",
          "stateCode": "19",
          "postalCodePrefixes": [ "52" ]
        },
        {
          "code": "98",
          "name": "No consta",
          "stateCode": "98",
          "postalCodePrefixes": [ "98" ]
        },
        {
          "code": "99",
          "name": "Altres/Diversos",
          "stateCode": "99",
          "postalCodePrefixes": [ "99" ]
        }
      ]
    }

  sequenceGeneratorEndpointConfig: |-
    "SequenceGeneratorGetNextEndpoint": {
      "Url": "http://itsequencegenerator.ite-ms/itsequencegenerator/api/v1",
      "HttpTimeoutInSeconds": 700,
      "PolicyTimeoutInSeconds": 250,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 10
    }

  CommercetoolClient: |-
    "Client": {
      "AuthorizationBaseAddress": "https://auth.europe-west1.gcp.commercetools.com/",
      "ProjectKey": "myflower-preprod",
      "ApiBaseAddress": "https://api.europe-west1.gcp.commercetools.com/"
    }

  SlackAlertConfig: |-
    "SlackAlert": {
      "DefaultChannel": "alerts-ms-es",
      "BotName": "Error Alert Bot",
      "MaxRetryAttempts": 3
    }

  serilogConfig: |-
    "Serilog": {
      "Using": [],
      "MinimumLevel": {
        "Default": "Information",
        "Override": {
          "Microsoft": "Warning",
          "System": "Information",
          "Elastic": "Warning",
          "Apm": "Warning"
        }
      },
      "WriteTo": [
        {
          "Name": "Console"
        }
      ],
      "Enrich": [
        "FromLogContext",
        "WithMachineName",
        "WithProcessId",
        "WithThreadId"
      ],
      "Properties": {
        "ApplicationName": "ite-itorderreactor"
      }
    }

env:
  - name: MongoDb__ConnectionString
    value: "mongodb://ite-ms-mongodb-0.ite-ms-mongodb-headless.ite-ms-common.svc.cluster.local:27017,ite-ms-mongodb-1.ite-ms-mongodb-headless.ite-ms-common.svc.cluster.local:27017,ite-ms-mongodb-2.ite-ms-mongodb-headless.ite-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: ElasticApm__Environment
    value: "ite-preprod"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "ite-itorderreactor"    

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "ite-ms-kafka-headless.ite-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "ite-ms-redis-headless.ite-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"
  
  - name: Unleash__Url
    value: "http://ite-unleash.ite-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "ite-itorderreactor"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Unleash__ProjectId
    value: "default"

  - name: Unleash__Environment
    value: "production"

  - name: FeatureManagement__ShutdownOnException
    value: "true"

#appsettings.Development.json
  - name: MongoDb__DatabaseNameSubset
    value: "it-florist"