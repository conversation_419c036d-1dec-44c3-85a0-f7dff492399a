﻿using IT.Microservices.OrderReactor.Domain;
using ITF.SharedLibraries.Kafka.Subscriber;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;

namespace IT.Microservices.OrderReactor.Infrastructure.Handler
{
    public class OrderHandler : KafkaBaseMessageHandler
    {
        private readonly IServiceScopeFactory _scopeFactory;

        public OrderHandler(IServiceScopeFactory scopeFactory)
        {
            _scopeFactory = scopeFactory;
        }

        public override async Task HandleMessage(object data, string topic = null, int? partition = null, long? offset = null)
        {
            using var scope = _scopeFactory.CreateScope();
            var myFacade = scope.ServiceProvider.GetRequiredService<IOrderFacade>();

            var result = data switch
            {
                LegacyOrderCreatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderAssignedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderCancelledMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderDeliveryTimeUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderDeliveryStatusUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderDeliveryCostUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderDeliveryDateUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderCardMessageUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderNotesUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderRecipientCoordinatesUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderDeliveryAddressUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderDeliveredOnBehalfMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderAcceptedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderRejectedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderDeliveredMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderAssignationRemovedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderAcceptedOnBehalfMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderRejectedOnBehalfMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderSentMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderRecipientNameUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderRecipientPhoneNumberUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderItemUpdatedMessage d => myFacade.Process(d, topic, partition, offset),
                LegacyOrderItemExecutorAmountUpdatedMessage d => myFacade.Process(d, topic, partition, offset),

                _ => Task.CompletedTask
            };

            await result;
        }
    }
}
