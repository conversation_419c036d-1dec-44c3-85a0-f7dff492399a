using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ITF.SharedLibraries.HealthCheck.Extensions;
using ITF.SharedLibraries.ApplicationMetrics.Extensions;
using Prometheus;
using System.Reflection;
using ITF.SharedLibraries.Readyness.Extensions;
using ITF.SharedLibraries.Swagger;
using ITF.SharedLibraries.Framework;
using ITF.SharedLibraries.Json;
using ITF.SharedLibraries.Kafka.Subscriber;
using ITF.SharedLibraries.Kafka.Extensions;
using static ITF.SharedLibraries.Kafka.DelegateHandler;
using ITF.SharedLibraries.FeatureFlags;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using IT.Microservices.OrderReactor.Infrastructure.Handler;
using IT.Microservices.OrderReactor.Domain;
using IT.Microservices.OrderReactor.Application;
using IT.Microservices.OrderReactor.Infrastructure;
using commercetools.Sdk.Api;
using System;
using ITF.SharedLibraries.HttpClient.Polly;
using static ITF.SharedLibraries.HttpClient.Polly.Extensions;
using ITF.SharedLibraries.MongoDB.Extensions;
using IT.SharedLibraries.CT.Settings;
using IT.SharedLibraries.CT.ProductTypes;
using IT.SharedLibraries.CT.Orders;
using IT.SharedLibraries.CT.Channels;
using IT.SharedLibraries.CT.Stores;
using IT.SharedLibraries.CT.Products;
using IT.SharedLibraries.CT.Carts;
using IT.SharedLibraries.CT.ShippingMethods;
using ITF.SharedLibraries.Kafka.Publisher;
using IT.Microservices.OrderReactor.Infrastructure.Settings;
using ITF.SharedLibraries.EnvironmentVariable;
using ITE.Order.Library.Infrastructure;
using ITF.Order.Library.Infrastructure;
using ITF.SharedLibraries.Alerting;

namespace IT.Microservices.OrderReactor
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<KafkaTopicsSettings>(Configuration.GetSection("KafkaTopicsSettings"));
            services.Configure<CommerceToolCustomSettings>(Configuration.GetSection("CommerceToolCustomSettings"));
            services.Configure<OrderReactorSettings>(Configuration.GetSection("OrderReactorSettings"));
            // Commercetools API
            var registry = services.AddPolicyRegistry();
            AddRetryPolicy(registry,
                backoff: new TimeSpan[]
                {
                    TimeSpan.FromMilliseconds(10),
                    TimeSpan.FromMilliseconds(20),
                    TimeSpan.FromMilliseconds(50),
                    TimeSpan.FromMilliseconds(100)
                },
                policyName: "CTRetryPolicy");
            AddTimeOutPolicy(registry,
                timeOut: TimeSpan.FromSeconds(120),
                policyName: "CTTimeOut");

            services.UseCommercetoolsApiSerialization();
            services.UseCommercetoolsApi(Configuration, "Client")
                .AddPolicyHandlerFromRegistry("CTRetryPolicy")
                .AddPolicyHandlerFromRegistry("CTTimeOut");

            // MongoDb
            services.UseMongoDb(Configuration);

            Setup(services);

            // Slack Alert Service
            services.AddSlackAlertService(Configuration);

            // Business
            services.AddSingleton<ISequenceGeneratorService, SequenceGeneratorService>();
            services.AddSingleton<IProductTypeService, ProductTypeService>();
            services.AddSingleton<IChannelService, ChannelService>();
            services.AddSingleton<IStoreService, StoreService>();
            services.AddSingleton<IProductService, ProductService>();
            services.AddSingleton<ICartService, CartService>();
            services.AddSingleton<IShippingMethodService, ShippingMethodService>();
            services.AddSingleton<IOrderService, OrderService>();
            services.AddSingleton<IFloristOrderPerDayRepository, FloristOrderPerDayRepository>();
            services.AddSingleton<IFloristsRepository, FloristsRepository>();
            services.AddSingleton<IOrderReactorIdempotenceRepository, OrderReactorIdempotenceRepository>();
            services.AddScoped<IOrderUseCase, OrderUseCase>();
            services.AddScoped<IOrderFacade, OrderFacade>();
            services.AddScoped<IKafkaPublisher, KafkaPublisher>();
            services.AddScoped<IKafkaPublisherHelper, KafkaPublisherHelper>();
            // Kafka
            services.UseKafkaPublisher(Configuration);
            services.UseKafkaHealthChecker(Configuration);

            // if FR country add a dedicated Kafka config with 2 Handlers aligned with the chartsValues that also have 2 subscribers defined in the settings (topic order for PFS and legacy_order for RAO france)
            if (Configuration.GetSection("CommerceToolCustomSettings:LocalCountryCode")?.Value?.ToUpper() == "FR")
                services.UseKafkaSubscribers<string, string>(Configuration,
                    kafkaActionHandlers: new KafkaDelegateHandler[] {
                        KafkaHandlerSupplier<IMessageHandler, OrderHandler>,
                        KafkaHandlerSupplier<IMessageHandler, RAOLegacyOrderHandler>
                    });
            else
                services.UseKafkaSubscribers<string, string>(Configuration,
                    kafkaActionHandlers: new KafkaDelegateHandler[] {
                        KafkaHandlerSupplier<IMessageHandler, OrderHandler>
                    });

            // HealthChecks
            services
                .AddHealthChecksMiddleware();

            // Metrics
            services.AddAllMetrics();
			
            // Feature Flags
            services.UseFeatureFlags(Configuration);
			
            services
                .AddControllers()
				.UsePascalCase()
                .SuppressAutoACR();

            // Swagger
            services.AddSwagger(Assembly.GetExecutingAssembly().GetName().Name);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IApiVersionDescriptionProvider provider)
        {           
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
			
            app.UseSwaggerEndpoint(provider, "itorderreactor");

            app.UseRouting();
			
			// Metrics middleware
            app.UseAllMetricsMiddleware()
                .UseMiddleware<RequestMiddleware>();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
				// HealthChecks
                endpoints.UseHealthChecks();

				// Readyness
                endpoints.UseReadynessRoute();
				
				// Metrics
                endpoints.MapMetrics();

                endpoints.MapControllers();
            });
        }

        private void Setup(IServiceCollection services)
        {
            var config = Configuration.Get<CommerceToolCustomSettings>("CommerceToolCustomSettings");
            if (config.LocalCountryCode == "ES" || config.LocalCountryCode == "PT")
            {
                services.UseSpanishOrderRules(Configuration);
            }
            else if (config.LocalCountryCode == "FR")
            {
                services.UseFrenchOrderRules(Configuration);
            }
        }
    }
}
